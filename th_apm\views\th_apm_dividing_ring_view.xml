<?xml version="1.0" encoding="UTF-8" ?>
<odoo>
    <record id="th_apm_dividing_ring_view_tree" model="ir.ui.view">
        <field name="name">th_apm_dividing_ring_view_tree</field>
        <field name="model">th.apm.dividing.ring</field>
        <field name="arch" type="xml">
            <tree string="">
                <field name="name"/>
            </tree>
        </field>
    </record>

    <record id="th_apm_dividing_ring_view_form" model="ir.ui.view">
        <field name="name">th_apm_dividing_ring_view_form</field>
        <field name="model">th.apm.dividing.ring</field>
        <field name="arch" type="xml">
            <form string="">
                <sheet>
                    <group>
                    <group>
                        <field name="name"/>
                        <field name="th_origin_id" domain="[('th_module_ids', 'in', 'th_check_domain_origin_ids')]"/>
                        <field name="th_flag" invisible="1"/>
                        <field name="th_check_domain_origin_ids" invisible="1"/>
                    </group>
                    <group>
                        <field name="th_is_partner_dividing"/>
                        <field name="th_is_opportunity_dividing"/>
                    </group>
                    </group>
                    <notebook>
                        <page string="Nhân viên" name="th_user">
                            <field name="th_user_ids" mode="kanban" class="w-100">
                                <kanban>
                                    <field name="id"/>
                                    <field name="name"/>
                                    <field name="email"/>
                                    <field name="avatar_128"/>
                                    <templates>
                                        <t t-name="kanban-box">
                                            <div class="oe_kanban_card oe_kanban_global_click">
                                                <div class="o_kanban_card_content d-flex">
                                                    <div>
                                                        <img t-att-src="kanban_image('res.users', 'avatar_128', record.id.raw_value)"
                                                             class="o_kanban_image o_image_64_cover" alt="Avatar"/>
                                                    </div>
                                                    <div class="oe_kanban_details d-flex flex-column ms-3">
                                                        <strong class="o_kanban_record_title oe_partner_heading">
                                                            <field name="name"/>
                                                        </strong>
                                                        <div class="d-flex align-items-baseline text-break">
                                                            <i class="fa fa-envelope me-1" role="img" aria-label="Email"
                                                               title="Email"/>
                                                            <field name="email"/>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <a type="delete"
                                               style="position: absolute; right: 0; padding: 4px; display: inline-block; color: black;"
                                               class="fa fa-trash">
                                                <span style="display:none">Delete</span>
                                            </a>
                                        </t>
                                    </templates>
                                </kanban>
                            </field>
                        </page>
                    </notebook>
                </sheet>
            </form>
        </field>
    </record>

    <record id="th_apm_dividing_ring_view_act" model="ir.actions.act_window">
        <field name="name">Vòng chia APM</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">th.apm.dividing.ring</field>
        <field name="view_mode">tree,form</field>
        <field name="help" type="html">
          <p class="oe_view_nocontent_create">
            <!-- Add Text Here -->
          </p><p>
            <!-- More details about what a user can do with this object will be OK -->
          </p>
        </field>
    </record>

</odoo>
