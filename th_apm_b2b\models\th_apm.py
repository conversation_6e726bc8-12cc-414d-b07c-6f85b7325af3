from odoo import fields, models, api, _
import json

class ThApm(models.Model):
    _inherit = "th.apm"
    readonly_domain = fields.Char(compute="_compute_readonly_domain")
    th_stage_b2b_id = fields.Many2one(related="th_stage_id")
    is_handed_over = fields.Boolean()
    th_owner_history_ids = fields.Many2many(comodel_name='th.ownership.unit', compute='_compute_th_owner_history',compute_sudo=True)
    th_user_ownership_ids = fields.Many2many(comodel_name='res.users', compute='_compute_th_owner_history', relation='th_user_ownership_ids_rel', store=True, compute_sudo=True)
    th_apm_b2b_dividing_ring_id = fields.Many2one('th.apm.dividing.ring', string="Vòng chia b2b", domain="th_apm_b2b_dividing_ring_domain")
    th_apm_b2b_dividing_ring_domain = fields.Char(compute="_compute_th_apm_b2b_dividing_ring_domain")

    # def _compute_readonly_domain(self):
    #     for rec in self:
    #         rec.readonly_domain = json.dumps(['|', ('th_is_a_duplicate_opportunity', '=', True), ('state', '=', 'transfer')])

    @api.depends('th_campaign_id')
    def _compute_th_apm_b2b_dividing_ring_domain(self):
        for record in self:
            if record.th_campaign_id:
                domain = [('id', 'in', record.th_campaign_id.th_apm_divide_ring_ids.ids),
                          ('th_is_partner_dividing', '=', True)]
            else:
                domain = []
            record.th_apm_b2b_dividing_ring_domain = json.dumps(domain)


    @api.onchange('th_apm_b2b_dividing_ring_id')
    def onchange_th_apm_b2b_dividing_ring_id(self):
        """Khi thay đổi vòng chia, tự động chia cơ hội và gán người thực hiện"""
        # Đánh dấu trạng thái vòng chia
        if self.th_apm_b2b_dividing_ring_id:
            try:
                # Gọi hàm phân chia cơ hội từ vòng chia
                user_id = self.th_apm_b2b_dividing_ring_id.action_assign_leads_dividing_ring()
                if user_id:
                    self.th_user_id = user_id
                self.th_apm_dividing_ring_id = self.th_apm_b2b_dividing_ring_id
            except Exception:
                # Xử lý lỗi nếu có - không gán người thực hiện
                self.th_user_id = False

    @api.depends('th_campaign_id')
    def _compute_th_campaign_domain(self):
        for record in self:
            domain = []
            if record.env.user.has_group('th_apm.group_leader_apm_after_order') and not record.env.user.has_group(
                    'th_apm.group_apm_leader'):
                campaign = self.env['th.apm.campaign'].search(
                    ['|', ('id', '=', record.env.ref('th_apm.campaign_after_sales_care').id),
                     ('th_check_group', '=', True), ('state', '=', 'approve')])
                domain.append(('id', 'in', campaign.ids))
            elif record.env.user.has_group('th_apm.group_apm_leader') or record.env.user.has_group(
                    'th_apm.group_apm_user'):
                campaign = self.env['th.apm.campaign'].search(
                    [('id', '!=', record.env.ref('th_apm.campaign_lead_formio').id),
                     ('id', '!=', record.env.ref('th_apm.campaign_lead_auto').id), ('state', '=', 'approve'),
                     ('th_origin_id', '!=', False)])
                if self._context.get('is_after_order'):
                    campaign = campaign + self.env['th.apm.campaign'].search(
                        [('id', '=', record.env.ref('th_apm.campaign_after_sales_care').id)])
                domain.append(('id', 'in', campaign.ids))
            elif record.env.user.has_group('th_apm_b2b.th_apm_group_partner_manager'):
                campaign = self.env['th.apm.campaign'].search(
                    [('id', '!=', record.env.ref('th_apm.campaign_lead_formio').id),
                     ('id', '!=', record.env.ref('th_apm.campaign_lead_auto').id), ('state', '=', 'approve'),
                     ('th_origin_id', '!=', False)])
                if self._context.get('is_after_order'):
                    campaign = campaign + self.env['th.apm.campaign'].search(
                        [('id', '=', record.env.ref('th_apm.campaign_after_sales_care').id)])
                domain.append(('id', 'in', campaign.ids))
            record.th_campaign_domain = json.dumps(domain)

    def _compute_readonly_domain(self):
        for rec in self:
            if rec.is_handed_over or rec.th_is_lead_TTVH:
                rec.readonly_domain = json.dumps([])
            else:
                rec.readonly_domain = False

    @api.depends('th_order_history_ids')
    def _compute_th_owner_history(self):
        for rec in self:
            rec.sudo().th_owner_history_ids = False
            rec.sudo().th_user_ownership_ids = False
            if rec.sudo().th_order_history_ids:
                list_owner = []
                list_user = []
                for res in rec.th_order_history_ids:
                    list_owner.append(res.th_ownership_unit_new_id.id)
                    if res.th_ownership_unit_new_id:
                        list_user += res.th_ownership_unit_new_id.th_user_apm_b2b_ids.ids
                rec.sudo().th_owner_history_ids = [(6, 0, list_owner)]
                rec.sudo().th_user_ownership_ids = [(6, 0, list_user)]

    def write(self, values):
        for rec in self:
            if self.env.user.id in rec.th_ownership_unit_id.th_user_apm_b2b_ids.ids and rec.is_handed_over == True or rec.th_is_lead_TTVH == True and self.env.user.id in rec.th_ownership_unit_id.th_user_apm_b2b_ids.ids\
                    or (self.env.user.id in rec.th_user_ownership_ids.ids):
                values.pop('th_last_check', None)
        if 'th_apm_dividing_ring_id' in values:
            values['th_apm_b2b_dividing_ring_id'] = values['th_apm_dividing_ring_id']
        result = super(ThApm, self).write(values)
        return result

    def action_view_sale_order_b2b(self):
        action = self.env["ir.actions.actions"]._for_xml_id("th_apm_b2b.th_apm_b2b_sale_orders_action")
        context = {
            'create': 0,
            'edit': 0,
            'delete': 0,
            'invisible_button': True,
            'view_apm': True,
            'th_is_apm_b2b': True,
        }
        domain = [('partner_id', '=', self.th_partner_id.id), ('th_apm_id', '=', self.id)]
        action['context'] = context
        action['domain'] = domain
        return action
        # self.ensure_one()
        # return {
        #     'type': 'ir.actions.act_window',
        #     'name': 'Đơn hàng',
        #     'view_mode': 'tree,form',
        #     # 'view_id': False,
        #     'res_model': 'sale.order',
        #     'domain': [('partner_id', '=', self.th_partner_id.id), ('th_apm_id', '=', self.id)],
        #     'views': [(self.env.ref('th_apm.view_order_tree').id, "tree")],
        #     'context': {'create': False, 'edit': False, 'th_is_apm_b2b': True},
        # }

    def action_open_apm_partner_b2b(self):
        self.ensure_one()
        return {
            'name': 'Khách hàng',
            'view_mode': 'form',
            'res_model': 'res.partner',
            'type': 'ir.actions.act_window',
            'target': 'new',
            'res_id': self.th_partner_id.id,
            'context': {'create': 0, 'edit': 0, 'th_is_partner': True},
            'domain': [('id', '=', self.th_partner_id.id)],
        }
    def th_action_hand_over_apm(self):
        return {
            'name': 'Chọn vòng chia',
            'view_mode': 'form',
            'res_model': "th.apm",
            'type': 'ir.actions.act_window',
            'views': [(self.env.ref('th_apm_b2b.th_apm_b2b_popup_view_form').id, 'form')],
            'target': 'new',
            'context': {'default_new_lead_id': self.id},
            'res_id': self.id
        }
    def th_accept_team_apm(self):
        self.is_handed_over = True
        if self.th_apm_b2b_dividing_ring_id and not self.th_user_id:
            try:
                user_id = self.th_apm_b2b_dividing_ring_id.action_assign_leads_dividing_ring()
                if user_id:
                    self.th_user_id = user_id
                self.th_apm_dividing_ring_id = self.th_apm_b2b_dividing_ring_id
            except Exception:
                self.th_user_id = False


